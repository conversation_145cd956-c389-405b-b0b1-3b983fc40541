import React, { useEffect, useMemo, useRef, useState } from "react";
import { Button } from "@/components/ui/button";
import { Separator } from "@/components/ui/separator";
import { getThread, markThreadRead, markThreadUnread, parseHeader } from "@/lib/gmail";
import { Mail, MailOpen, ArrowLeft } from "lucide-react";
import { useVirtualizer } from "@tanstack/react-virtual";

type Props = {
  threadId: string;
  onBack?: () => void;
};

type ThreadMessageView = {
  id: string;
  from?: string;
  to?: string;
  date?: string;
  subject?: string;
  html?: string;
  text?: string;
  unread?: boolean;
};

function decodeBase64Url(data?: string): string | undefined {
  if (!data) return undefined;
  try {
    const normalized = data.replace(/-/g, "+").replace(/_/g, "/");
    // atob handles base64 (not urlsafe); add padding if needed
    const padLength = (4 - (normalized.length % 4)) % 4;
    const padded = normalized + "=".repeat(padLength);
    const binary = atob(padded);
    const bytes = new Uint8Array(binary.length);
    for (let i = 0; i < binary.length; i++) bytes[i] = binary.charCodeAt(i);
    const decoder = new TextDecoder("utf-8");
    return decoder.decode(bytes);
  } catch {
    return undefined;
  }
}

function extractBodies(payload: any): { html?: string; text?: string } {
  if (!payload) return {};
  // Direct body
  const mimeType: string | undefined = payload.mimeType;
  const bodyData: string | undefined = payload.body?.data;
  if (mimeType === "text/html" && bodyData) return { html: decodeBase64Url(bodyData) };
  if (mimeType === "text/plain" && bodyData) return { text: decodeBase64Url(bodyData) };
  // Search parts DFS
  const stack: any[] = [...(payload.parts || [])];
  let text: string | undefined;
  let html: string | undefined;
  while (stack.length) {
    const p = stack.shift();
    const mt = p?.mimeType;
    const bd = p?.body?.data;
    if (mt === "text/html" && bd && !html) html = decodeBase64Url(bd);
    if (mt === "text/plain" && bd && !text) text = decodeBase64Url(bd);
    if (p?.parts) stack.push(...p.parts);
    if (html && text) break;
  }
  return { html, text };
}

export function ThreadDetail({ threadId, onBack }: Props) {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | undefined>();
  const [messages, setMessages] = useState<ThreadMessageView[]>([]);
  const [threadUnread, setThreadUnread] = useState<boolean | undefined>(undefined);

  const refresh = async () => {
    setLoading(true);
    setError(undefined);
    try {
      const t = await getThread(threadId, {
        format: "full",
        // Partial response to reduce payload sizes
        fields:
          "id,messages(id,labelIds,internalDate,snippet,payload/headers(name,value),payload/mimeType,payload/body/data,payload/parts(mimeType,body/data,headers(name,value)))",
      });
      const views: ThreadMessageView[] = (t.messages || []).map((m: any) => {
        const headers = m.payload?.headers || [];
        const from = parseHeader(headers, "From");
        const to = parseHeader(headers, "To");
        const date = parseHeader(headers, "Date");
        const subject = parseHeader(headers, "Subject");
        const { html, text } = extractBodies(m.payload);
        const unread = (m.labelIds || []).includes("UNREAD");
        return { id: m.id, from, to, date, subject, html, text, unread };
      });
      setMessages(views);
      setThreadUnread(views.some((v) => v.unread));
    } catch (e: any) {
      setError(e?.message || String(e));
      setMessages([]);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    refresh();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [threadId]);

  const threadSubject = useMemo(() => {
    // Prefer subject from the last message that has it
    for (let i = messages.length - 1; i >= 0; i--) {
      const s = messages[i].subject;
      if (s) return s;
    }
    return "(no subject)";
  }, [messages]);

  return (
    <div className="flex flex-col gap-2">
      <div className="flex items-center gap-2">
        <Button size="sm" variant="ghost" className="h-7" onClick={onBack}>
          <ArrowLeft className="size-4" />
        </Button>
        <div className="text-sm font-medium truncate flex-1" title={threadSubject}>
          {threadSubject}
        </div>
        <Button
          size="sm"
          variant="ghost"
          className="h-7"
          title={threadUnread ? "Mark thread as read" : "Mark thread as unread"}
          onClick={async () => {
            try {
              if (threadUnread) await markThreadRead(threadId);
              else await markThreadUnread(threadId);
              setThreadUnread(!threadUnread);
              // Optimistically update messages' unread flags to match thread state
              setMessages((prev) => prev.map((m) => ({ ...m, unread: !threadUnread ? true : false })));
              try { chrome.runtime.sendMessage({ type: "mail/updated" }); } catch {}
            } catch (e) {
              // ignore
            }
          }}
        >
          {threadUnread ? <Mail className="size-4" /> : <MailOpen className="size-4" />}
        </Button>
      </div>
      <Separator />
      {loading && <div className="px-2 py-2 text-xs opacity-70">Loading thread…</div>}
      {error && <div className="px-2 py-2 text-xs text-red-600">{error}</div>}
      {!loading && !error && (
        <ThreadMessagesVirtual messages={messages} />
      )}
    </div>
  );
}

function ThreadMessagesVirtual({ messages }: { messages: ThreadMessageView[] }) {
  const parentRef = useRef<HTMLDivElement>(null);
  const rowVirtualizer = useVirtualizer({
    count: messages.length,
    getScrollElement: () => parentRef.current,
    estimateSize: () => 240, // average message card height
    overscan: 5,
  });

  return (
    <div ref={parentRef} className="h-[calc(100vh-180px)] overflow-auto pr-3">
      <div style={{ height: rowVirtualizer.getTotalSize(), position: "relative" }}>
        {rowVirtualizer.getVirtualItems().map((vi) => {
          const m = messages[vi.index];
          return (
            <div
              key={m.id}
              data-index={vi.index}
              ref={rowVirtualizer.measureElement}
              style={{
                position: "absolute",
                top: 0,
                left: 0,
                width: "100%",
                transform: `translateY(${vi.start}px)`,
              }}
              className="rounded-md border border-border mb-3"
            >
              <div className="px-3 py-2 text-xs flex flex-wrap gap-2 border-b border-border bg-[color-mix(in_oklab,var(--color-background),#000_3%)]">
                <div><span className="opacity-70">From:</span> {m.from}</div>
                <div><span className="opacity-70">To:</span> {m.to}</div>
                <div><span className="opacity-70">Date:</span> {m.date}</div>
              </div>
              <div className="px-3 py-3 text-sm">
                {m.html ? (
                  <div dangerouslySetInnerHTML={{ __html: m.html }} />
                ) : m.text ? (
                  <pre className="whitespace-pre-wrap text-xs opacity-90">{m.text}</pre>
                ) : (
                  <div className="text-xs opacity-70">(no content)</div>
                )}
              </div>
            </div>
          );
        })}
      </div>
    </div>
  );
}
