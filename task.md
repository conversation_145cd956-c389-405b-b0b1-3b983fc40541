# task.md — Roadmap & Execution Plan

## Phase 0 — Project Bootstrap (Chrome MV3 + React/Vite + Tailwind v4 + shadcn + Bun)
- Tasks
  - Initialize extension app with Vite + React using Bun.
  - Add `crxjs/vite-plugin` and configure MV3 build (background service worker, side panel HTML/TSX entry, options page).
  - Configure Tailwind CSS v4 with `@tailwindcss/vite`; add base theme tokens and dark mode.
  - Integrate shadcn/ui (CLI) for sidebar layout, navigation, dialogs, toasts, tables.
  - Add ESLint/TS config; set up `bun run dev`/`bun run build` scripts.
  - Create `manifest.json` with: `manifest_version: 3`, `permissions: ["identity", "storage", "sidePanel"]`,
    `host_permissions: ["https://*.googleapis.com/*", "https://accounts.google.com/*", "https://oauth2.googleapis.com/*"]`,
    `background.service_worker`, `side_panel.default_path`.
- Deliverables
  - Runnable dev build: side panel opens, renders placeholder UI.
- Acceptance Criteria
  - `bunx vite` serves extension dev, side panel present; build outputs `dist/` with MV3 artifacts.

## Phase 1 — OAuth (Multi-Account) via chrome.identity.launchWebAuthFlow
- Tasks
  - Create Google Cloud OAuth client (Web app) with redirect: `https://<EXTENSION_ID>.chromiumapp.org/`.
  - Implement `launchWebAuthFlow` PKCE flow in background worker; parse code, exchange tokens.
  - Store tokens per account (`chrome.storage`) with minimal retention; implement refresh flow.
  - Account switcher UI in side panel; show email/avatar; sign out per account.
- Deliverables
  - Multiple Gmail accounts can be linked and switched.
- Acceptance Criteria
  - Can link 2+ accounts; switching updates inbox context; tokens refresh before expiry; secure handling (no logs of tokens).

## Phase 2 — Gmail Basics: Labels, Threads, Messages, Read/Unread
- Tasks
  - Call Gmail API with least-privilege scopes:
    - Read-only: `https://www.googleapis.com/auth/gmail.readonly` (expand later as needed).
  - Implement `users.labels.list/get`, `users.threads.list/get`, `users.messages.get`.
  - Category filters (Primary/Social/Promotions), unread badge, pagination.
  - Mark read/unread via `users.threads.modify` or `users.messages.batchModify` (requires `gmail.modify`).
- Deliverables
  - Sidebar lists inbox with unread status and category filters; thread detail view.
- Acceptance Criteria
  - 50 latest threads load < 600ms (post-token); read/unread toggles reflected after refresh.

## Phase 3 — Compose/Send/Drafts/Attachments
- Tasks
  - Scopes: add `gmail.compose`, `gmail.send`; drafts via `users.drafts.create/update/send`.
  - Compose UI (modal/drawer), to/cc/bcc, subject, body (rich text/markdown), attachment upload.
  - Sending with MIME base64 `message.raw`; attachments via `users.messages.attachments.get` for viewing.
- Deliverables
  - Send new email, reply/forward; save draft; download/preview attachments.
- Acceptance Criteria
  - Successfully send email to a test account; attachment integrity preserved.

## Phase 4 — Labels, Batch Actions, Trash Management
- Tasks
  - `users.labels.create`, apply/remove via `users.messages.batchModify` / `users.threads.modify`.
  - Batch selection in list; quick actions (archive, label, mark read/unread, delete).
  - Trash and Empty Trash action (requires confirmation modal).
- Deliverables
  - Bulk triage flows available in UI.
- Acceptance Criteria
  - Batch labeling and delete actions succeed for 10+ messages; empty trash completes with confirmation.

## Phase 5 — Cloudflare Agent Backend (AI Tools + Gateway)
- Tasks
  - Create Cloudflare Worker using Agents SDK. Expose tools mapping Gmail operations: `gmail.list`, `gmail.getThread`,
    `gmail.modifyLabels`, `gmail.send`, `gmail.draft`, `gmail.attachments.get`, `gmail.trash`, `gmail.emptyTrash`.
  - Human-in-the-loop approval for destructive tools (delete/empty trash) using Agents Tool Registry.
  - Configure AI Gateway Universal Endpoint with provider `compat`, endpoint `chat/completions`.
  - Set primary model: `google-ai-studio/gemini-2.5-flash-lite`; define fallback chain (e.g., Workers AI `@cf/meta/llama-3.1-8b-instruct`).
  - Streaming responses; connect side panel chat UI to Worker SSE/WebSocket where applicable.
- Deliverables
  - Deployed Worker endpoint (dev) with tools and AI integration; local streaming to UI.
- Acceptance Criteria
  - AI can summarize a thread, propose labels, generate reply draft; approvals required for delete/empty trash.

## Phase 6 — AI UX in the Sidebar
- Tasks
  - Add AI panel: chat history, tool call visualization, approval prompts.
  - Preset prompts: summarize, extract tasks, smart search (convert NL → Gmail query), draft professional reply.
  - Result injection back into UI (apply labels, create draft) after user approves.
- Deliverables
  - End-to-end AI workflow from query → tool suggestion → approval → action.
- Acceptance Criteria
  - Summarization p50 < 2.5s for short threads; tool results reflected in the UI.

## Phase 7 — Schedule Send
- Tasks
  - Backend scheduling using Cloudflare Workflows or Durable Objects (preferred over `chrome.alarms`).
  - UI for pickers; list scheduled items; cancel/edit.
- Deliverables
  - Reliable schedule send independent of browser state.
- Acceptance Criteria
  - Email scheduled 5 minutes ahead sends successfully even if browser is closed.

## Phase 8 — Observability, Security, Privacy
- Tasks
  - AI Gateway analytics, retries/backoff config; redact logs; rate limiting.
  - Token storage review; consider backend encryption if storing refresh tokens server-side.
  - Privacy controls: toggles for AI features; per-thread explicit consent before sending content to AI.
- Deliverables
  - Basic dashboards/metrics; policy docs; privacy UI.
- Acceptance Criteria
  - No PII logged; toggles persist; retry headers/config applied.

## Phase 9 — Packaging & Store Submission
- Tasks
  - Production build; zip `dist/` for store upload.
  - Prepare store listing: description, screenshots, permissions rationale, privacy policy.
  - Internal QA checklist; beta testers.
- Deliverables
  - Store-ready package and listing assets.
- Acceptance Criteria
  - Pass Chrome Web Store automated checks; clear permission explanations.

---

## Implementation Details & References
- OAuth (MV3)
  - Use `chrome.identity.launchWebAuthFlow` with `interactive` true. Handle `abortOnLoadForNonInteractive`, `timeoutMsForNonInteractive` for robustness.
  - Redirect URI pattern: `https://<EXTENSION_ID>.chromiumapp.org/`.
- Gmail API Scopes & Methods (least privilege first)
  - Read: `gmail.readonly`, Metadata: `gmail.metadata`, Modify: `gmail.modify`, Labels: `gmail.labels`, Compose/Send/Drafts.
  - Methods: `users.threads.list/get/modify`, `users.messages.list/get/batchModify`, `users.labels.list/get/create`, `users.drafts.create/update/send`, `users.messages.send`, `users.messages.attachments.get`.
- AI Gateway Universal Endpoint
  - Universal request with `provider: "compat"`, `endpoint: "chat/completions"`, `query.model: "google-ai-studio/gemini-2.5-flash-lite"`.
  - Configure retries/backoff; optional WebSocket mode for realtime.
- Tailwind v4 + Vite
  - Use `@tailwindcss/vite` plugin; CSS-first theme via `@import "tailwindcss"` and `@theme` for tokens.
- shadcn/ui
  - Use `npx shadcn@latest add ...` to import components; store in `components/` for full control.
- Cloudflare Agents Starter
  - Follow structure: `app.tsx` (UI), `server.ts` (agent), `tools.ts` (tools), streaming patterns, tool approvals.

---

## Commands (Bun)
- Bootstrap
  - `bun create vite my-ext --template react-ts`
  - `bun add -D vite vite-plugin-web-extension @tailwindcss/vite tailwindcss`
  - `bun add webextension-polyfill class-variance-authority lucide-react` (as needed)
  - `bunx shadcn@latest init` and `bunx shadcn@latest add button dialog sheet table toast`
- Dev & Build
  - `bunx vite` (dev), `bun run build` (prod)
- Cloudflare
  - `bun add -D wrangler` and configure Worker/Agents

---

## Definition of Done (per Phase)
- Unit tests for utility functions.
- Manual E2E checklist for auth, listing, actions, AI flows.
- Accessibility audit (keyboard navigation, contrast).
- Performance spot-checks (FMP, latency, payload sizes).

## Risks & Mitigations (Quick)
- OAuth flow breaks due to redirect mismatch → Verify redirect exact match; test with dummy accounts.
- Gmail quotas → Use pagination, `batchModify`, cache labels, backoff on 429.
- Data privacy → Opt-in per-thread AI processing; redact/summarize client-side when possible.
