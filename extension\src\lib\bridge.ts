import type { AccountsState } from "./types";

function sendMessage<TReq, TRes = any>(payload: TReq): Promise<TRes> {
  return new Promise((resolve, reject) => {
    try {
      chrome.runtime.sendMessage(payload, (response) => {
        const err = chrome.runtime.lastError;
        if (err) return reject(new Error(err.message));
        resolve(response as TRes);
      });
    } catch (e) {
      reject(e);
    }
  });
}

export function listAccounts(): Promise<{ ok: boolean; state?: AccountsState; error?: string }> {
  return sendMessage({ type: "accounts/list" });
}

export function addAccount(): Promise<{ ok: boolean; state?: AccountsState; error?: string }> {
  return sendMessage({ type: "accounts/add" });
}

export function switchAccount(accountId?: string): Promise<{ ok: boolean; state?: AccountsState; error?: string }> {
  return sendMessage({ type: "accounts/switch", accountId });
}

export function signOut(accountId?: string): Promise<{ ok: boolean; state?: AccountsState; error?: string }> {
  return sendMessage({ type: "accounts/signout", accountId });
}

export function getActiveAccessToken(): Promise<{ ok: boolean; accessToken?: string; error?: string }> {
  return sendMessage({ type: "tokens/getActive" });
}
