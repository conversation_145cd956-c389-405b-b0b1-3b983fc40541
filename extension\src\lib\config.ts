export const GOOG<PERSON>_CLIENT_ID = "************-1o1g4baignku7saj2pd7t5iaiv60g8uk.apps.googleusercontent.com";

export const GOOGLE_AUTH_ENDPOINT = "https://accounts.google.com/o/oauth2/v2/auth";
export const GOOGLE_TOKEN_ENDPOINT = "https://oauth2.googleapis.com/token";
export const GOOGLE_REVOKE_ENDPOINT = "https://oauth2.googleapis.com/revoke";
export const GMAIL_PROFILE_ENDPOINT = "https://gmail.googleapis.com/gmail/v1/users/me/profile";
export const OPENID_USERINFO_ENDPOINT = "https://openidconnect.googleapis.com/v1/userinfo";

// Scopes: OIDC for avatar/email + least-privilege Gmail read-only (expand later phases)
export const OAUTH_SCOPES_ARRAY = [
  "openid",
  "email",
  "profile",
  // Phase 2: include modify to allow marking read/unread, label changes
  "https://www.googleapis.com/auth/gmail.readonly",
  "https://www.googleapis.com/auth/gmail.modify",
  // Phase 3: compose/send/drafts scopes
  "https://www.googleapis.com/auth/gmail.compose",
  "https://www.googleapis.com/auth/gmail.send",
  // People API (read-only contacts) for contact photos
  "https://www.googleapis.com/auth/contacts.readonly",
  // Other Contacts (auto-saved) read-only for wider coverage
  "https://www.googleapis.com/auth/contacts.other.readonly",
];
export const OAUTH_SCOPES = OAUTH_SCOPES_ARRAY.join(" ");

export const TOKEN_EXPIRY_SKEW_SECONDS = 60; // refresh 1 minute early

// Backend integration (Option B): server-side token exchange to keep client_secret safe
// For dev, use local Worker dev server
export const USE_BACKEND_TOKEN_EXCHANGE = true;
export const BACKEND_BASE_URL = "https://gmail-agent-backend.vudinhhiep3004.workers.dev"; // e.g., https://your-worker.workers.dev in prod
