import { getActiveAccessToken } from "./bridge";

const GMAIL_BASE = "https://gmail.googleapis.com/gmail/v1/users/me" as const;

type GmailThreadListResponse = {
  threads?: { id: string; snippet?: string; historyId?: string }[];
  nextPageToken?: string;
  resultSizeEstimate?: number;
};

type GmailMessageHeader = { name: string; value: string };

type GmailMessage = {
  id: string;
  threadId: string;
  labelIds?: string[];
  snippet?: string;
  internalDate?: string;
  payload?: {
    headers?: GmailMessageHeader[];
  };
};

type GmailThread = {
  id: string;
  snippet?: string;
  historyId?: string;
  messages?: GmailMessage[];
};

function buildUrl(path: string, params?: Record<string, string | number | boolean | undefined>) {
  const url = new URL(`${GMAIL_BASE}${path}`);
  if (params) {
    for (const [k, v] of Object.entries(params)) {
      if (v === undefined) continue;
      url.searchParams.set(k, String(v));
    }
  }
  return url;
}

async function authFetch(url: URL): Promise<Response> {
  const resToken = await getActiveAccessToken();
  if (!resToken.ok || !resToken.accessToken) {
    throw new Error(resToken.error || "Missing access token");
  }
  const res = await fetch(url, {
    headers: { Authorization: `Bearer ${resToken.accessToken}` },
  });
  if (!res.ok) {
    const text = await res.text();
    throw new Error(`Gmail API ${url.pathname} failed: ${res.status} ${text}`);
  }
  return res;
}

async function authFetchWithBody(url: URL, init: { method?: string; body?: any; headers?: Record<string, string> } = {}): Promise<Response> {
  const resToken = await getActiveAccessToken();
  if (!resToken.ok || !resToken.accessToken) {
    throw new Error(resToken.error || "Missing access token");
  }
  const headers: Record<string, string> = {
    Authorization: `Bearer ${resToken.accessToken}`,
    "Content-Type": "application/json",
    ...(init.headers || {}),
  };
  const res = await fetch(url, {
    method: init.method || "POST",
    headers,
    body: init.body ? JSON.stringify(init.body) : undefined,
  });
  if (!res.ok) {
    const text = await res.text();
    throw new Error(`Gmail API ${url.pathname} failed: ${res.status} ${text}`);
  }
  return res;
}

export async function listLabels() {
  const url = buildUrl("/labels");
  const res = await authFetch(url);
  const json = await res.json();
  return json;
}

export async function listThreads(options?: {
  q?: string;
  labelIds?: string[];
  maxResults?: number; // default 25
  pageToken?: string;
  fields?: string; // partial response selector
  includeSpamTrash?: boolean; // include SPAM/TRASH when needed (e.g., Spam view)
}) {
  const url = buildUrl("/threads", {
    maxResults: options?.maxResults ?? 25,
    q: options?.q,
    pageToken: options?.pageToken,
    includeSpamTrash: options?.includeSpamTrash ?? false,
    ...(options?.fields ? { fields: options.fields } : {}),
  });
  // Gmail expects repeated labelIds params (labelIds=INBOX&labelIds=CATEGORY_PERSONAL), not comma-separated
  if (options?.labelIds && options.labelIds.length > 0) {
    for (const id of options.labelIds) {
      if (id) url.searchParams.append("labelIds", id);
    }
  }
  const res = await authFetch(url);
  const json = (await res.json()) as GmailThreadListResponse;
  return json;
}

export async function getThread(
  threadId: string,
  opts?: { format?: "full" | "metadata"; headers?: string[]; fields?: string }
) {
  const url = buildUrl(`/threads/${threadId}`, {
    format: opts?.format ?? "metadata",
    ...(opts?.fields ? { fields: opts.fields } : {}),
  });
  for (const h of opts?.headers ?? ["Subject", "From", "To", "Date"]) {
    url.searchParams.append("metadataHeaders", h);
  }
  const res = await authFetch(url);
  const json = (await res.json()) as GmailThread;
  return json;
}

export function parseHeader(headers: GmailMessageHeader[] | undefined, name: string): string | undefined {
  const h = headers?.find((x) => x.name.toLowerCase() === name.toLowerCase());
  return h?.value;
}

export function computeThreadPreview(t: GmailThread) {
  const msgs = t.messages ?? [];
  const last = msgs[msgs.length - 1];
  const headers = last?.payload?.headers ?? [];
  const subject = parseHeader(headers, "Subject") ?? "(no subject)";
  const from = parseHeader(headers, "From") ?? "";
  const date = parseHeader(headers, "Date") ?? "";
  const snippet = last?.snippet ?? t.snippet ?? "";
  const unread = msgs.some((m) => m.labelIds?.includes("UNREAD"));
  return { subject, from, date, snippet, unread };
}

// ---- Phase 2: Modify thread labels (mark read/unread) & label helpers ----

export type ModifyLabelsPayload = {
  addLabelIds?: string[];
  removeLabelIds?: string[];
};

export async function modifyThread(threadId: string, payload: ModifyLabelsPayload) {
  const url = buildUrl(`/threads/${threadId}/modify`);
  const res = await authFetchWithBody(url, { method: "POST", body: payload });
  return res.json();
}

export async function markThreadRead(threadId: string) {
  // Remove UNREAD label
  return modifyThread(threadId, { removeLabelIds: ["UNREAD"] });
}

export async function markThreadUnread(threadId: string) {
  // Add UNREAD label
  return modifyThread(threadId, { addLabelIds: ["UNREAD"] });
}

export async function getLabel(labelId: string) {
  const url = buildUrl(`/labels/${labelId}`);
  const res = await authFetch(url);
  return res.json();
}

export const CATEGORY_LABEL_IDS = {
  primary: "CATEGORY_PERSONAL",
  social: "CATEGORY_SOCIAL",
  promotions: "CATEGORY_PROMOTIONS",
  updates: "CATEGORY_UPDATES",
  forums: "CATEGORY_FORUMS",
  spam: "SPAM",
} as const;

// ---- Phase 3: Drafts / Send / Attachments ----

type DraftResource = {
  id?: string;
  message?: { raw?: string };
};

export async function createDraft(rawBase64Url: string) {
  const url = buildUrl("/drafts");
  const res = await authFetchWithBody(url, { method: "POST", body: { message: { raw: rawBase64Url } } });
  return res.json();
}

export async function updateDraft(draftId: string, rawBase64Url: string) {
  const url = buildUrl(`/drafts/${draftId}`);
  const res = await authFetchWithBody(url, { method: "PUT", body: { message: { raw: rawBase64Url } } });
  return res.json();
}

export async function sendDraft(draftId: string, rawBase64Url?: string) {
  const url = buildUrl("/drafts/send");
  const draft: DraftResource = { id: draftId };
  if (rawBase64Url) draft.message = { raw: rawBase64Url };
  const res = await authFetchWithBody(url, { method: "POST", body: draft });
  return res.json();
}

export async function sendMessage(rawBase64Url: string) {
  const url = buildUrl("/messages/send");
  const res = await authFetchWithBody(url, { method: "POST", body: { raw: rawBase64Url } });
  return res.json();
}

export async function getAttachment(messageId: string, attachmentId: string) {
  const url = buildUrl(`/messages/${messageId}/attachments/${attachmentId}`);
  const res = await authFetch(url);
  return res.json();
}
