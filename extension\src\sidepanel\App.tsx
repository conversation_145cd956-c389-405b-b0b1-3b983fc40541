import React, { useState } from "react";
import { Sidebar } from "../components/layout/Sidebar";
import { Topbar } from "../components/layout/Topbar";
import { Separator } from "../components/ui/separator";
import { MailList } from "../components/mailbox/MailList";
import { ThreadDetail } from "../components/mailbox/ThreadDetail";

export default function App() {
  const [selectedThreadId, setSelectedThreadId] = useState<string | null>(null);

  return (
    <div className="min-h-screen bg-background text-foreground grid grid-cols-[1fr_44px]">
      {/* Main content area - left side */}
      <div className="flex flex-col min-h-screen border-r border-border">
        <Topbar />
        <div className="px-3 py-2">
          <div className="text-sm font-medium mb-1">{selectedThreadId ? "Thread" : "Inbox"}</div>
          <Separator className="my-2" />
          {selectedThreadId ? (
            <ThreadDetail threadId={selectedThreadId} onBack={() => setSelectedThreadId(null)} />
          ) : (
            <MailList onOpenThread={(id) => setSelectedThreadId(id)} />
          )}
        </div>
      </div>
      {/* Navigation sidebar - right side */}
      <Sidebar />
    </div>
  );
}
