import React, { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import { Search, Edit3 } from "lucide-react";

export function Topbar() {
  const [showSearch, setShowSearch] = useState(false);

  return (
    <div className="flex items-center gap-2 border-b border-border px-3 py-2">
      <div className="flex-1 flex items-center gap-2">
        {showSearch ? (
          <Input 
            placeholder="Search mail or type a command..." 
            autoFocus
            onBlur={() => setShowSearch(false)}
            className="flex-1"
          />
        ) : (
          <TooltipProvider disableHoverableContent>
            <Tooltip>
              <TooltipTrigger asChild>
                <Button 
                  size="sm" 
                  variant="ghost" 
                  onClick={() => setShowSearch(true)}
                  className="w-8 h-8 p-0"
                >
                  <Search className="size-4" />
                </Button>
              </TooltipTrigger>
              <TooltipContent>Search mail</TooltipContent>
            </Tooltip>
          </TooltipProvider>
        )}
      </div>
      <TooltipProvider disableHoverableContent>
        <Tooltip>
          <TooltipTrigger asChild>
            <Button size="sm" variant="secondary" className="w-8 h-8 p-0">
              <Edit3 className="size-4" />
            </Button>
          </TooltipTrigger>
          <TooltipContent>Compose</TooltipContent>
        </Tooltip>
      </TooltipProvider>
    </div>
  );
}
