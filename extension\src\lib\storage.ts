import type { Account, AccountsState } from "./types";

const STORAGE_KEY = "accountsState" as const;

function storageGet<T = unknown>(key: string): Promise<Record<string, T>> {
  return new Promise((resolve, reject) => {
    try {
      chrome.storage.local.get(key, (result) => {
        const err = chrome.runtime.lastError;
        if (err) return reject(new Error(err.message));
        resolve(result as Record<string, T>);
      });
    } catch (e) {
      reject(e);
    }
  });
}

function storageSet(items: Record<string, unknown>): Promise<void> {
  return new Promise((resolve, reject) => {
    try {
      chrome.storage.local.set(items, () => {
        const err = chrome.runtime.lastError;
        if (err) return reject(new Error(err.message));
        resolve();
      });
    } catch (e) {
      reject(e);
    }
  });
}

export async function getAccountsState(): Promise<AccountsState> {
  const obj = await storageGet<AccountsState>(STORAGE_KEY);
  const state = (obj[STORAGE_KEY] ?? { accounts: [] }) as AccountsState;
  if (!state.accounts) state.accounts = [];
  return state;
}

export async function setAccountsState(state: AccountsState): Promise<void> {
  await storageSet({ [STORAGE_KEY]: state });
}

export async function upsertAccount(account: Account, makeActive = true): Promise<AccountsState> {
  const state = await getAccountsState();
  const idx = state.accounts.findIndex((a) => a.id === account.id);
  if (idx >= 0) state.accounts[idx] = account;
  else state.accounts.push(account);
  if (makeActive) state.activeAccountId = account.id;
  await setAccountsState(state);
  return state;
}

export async function removeAccount(accountId: string): Promise<AccountsState> {
  const state = await getAccountsState();
  state.accounts = state.accounts.filter((a) => a.id !== accountId);
  if (state.activeAccountId === accountId) state.activeAccountId = state.accounts[0]?.id;
  await setAccountsState(state);
  return state;
}

export async function setActiveAccount(accountId: string | undefined): Promise<AccountsState> {
  const state = await getAccountsState();
  state.activeAccountId = accountId;
  await setAccountsState(state);
  return state;
}

export async function findAccount(accountId: string): Promise<Account | undefined> {
  const state = await getAccountsState();
  return state.accounts.find((a) => a.id === accountId);
}
