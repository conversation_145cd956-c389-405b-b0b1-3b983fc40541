# PRD — Gmail AI Agent Sidebar Chrome Extension

## 1. Overview
- Build a Chrome Extension (Manifest V3) with a sidebar UI (Side Panel) using React + Vite, Tailwind CSS v4, shadcn/ui, package manager Bun.
- Connect multiple Gmail accounts via OAuth (chrome.identity.launchWebAuthFlow) and integrate Gmail API to list, filter, search, read/unread, label, delete, draft, send emails, handle attachments, empty trash, and schedule send.
- Integrate an AI Agent backend on Cloudflare Agents using Cloudflare AI Gateway Universal Endpoint with Gemini 2.5 Flash Lite as primary model. The agent exposes tools (Gmail operations) with human-in-the-loop approval.
- Reference architecture and best practices from Cloudflare Agents Starter (cloudflare/agents-starter) for agent runtime, tool registry, streaming, scheduling, and UI patterns.

## 2. Goals & Non-Goals
- Goals
  - Multi-account Gmail linking, mailbox listing with categories, status (read/unread).
  - Full mailbox actions: read/unread, delete, label, search, compose, send, drafts, attachments, trash management, schedule send.
  - AI Agent supports: summarize mailbox/conversation, smart search, generate replies, classify/tag emails, batch triage, tool calling with approval.
  - Reliable OAuth for MV3 extensions with `launchWebAuthFlow`, token refresh, least-privilege scopes.
  - Performant, accessible sidebar UI with shadcn/ui + Tailwind v4.
  - Cloudflare ecosystem: Agents SDK, AI Gateway Universal Endpoint, optional Workflows for scheduling.
- Non-Goals (v1)
  - Offline mail caching beyond Gmail API pagination.
  - Admin/GWS domain-wide delegation.
  - Non-Gmail providers.

## 3. Personas
- Individual power user: Needs quick triage, bulk operations, smart summaries.
- PM/Founder: Multi-account oversight, AI summaries, scheduled sends.
- Support/Ops: Fast search, label automation, attachment previews, templates.

## 4. User Stories
- As a user, I can sign in multiple Gmail accounts and switch between them in the sidebar.
- I can view categorized inbox (Primary, Social, Promotions) and unread counts.
- I can filter, full-text search, and sort (date, unread first).
- I can mark read/unread, label, archive, delete, and empty trash.
- I can open a thread, view participants, timeline, and attachments.
- I can compose a new email, reply/forward, attach files, and send.
- I can draft emails and schedule sends.
- I can use AI to summarize threads, extract action items, auto-label, generate replies, and batch triage with preview/approval.

## 5. Functional Requirements
- Gmail API Integration
  - Scopes (choose least privilege for required features):
    - Read-only: `https://www.googleapis.com/auth/gmail.readonly`
    - Modify: `https://www.googleapis.com/auth/gmail.modify`
    - Labels: `https://www.googleapis.com/auth/gmail.labels`
    - Compose/Send/Drafts: `https://www.googleapis.com/auth/gmail.compose`, `https://www.googleapis.com/auth/gmail.send`
    - Full (if needed): `https://mail.google.com/` (avoid unless unavoidable)
  - Endpoints: `users.messages.list/get/batchModify`, `users.threads.list/get/modify`, `users.labels.list/get/create`, `users.drafts.create/update/send`, `users.messages.send`, `users.messages.attachments.get`
  - Multi-account: maintain separate token sets and per-account state. Use Chrome Storage + in-memory cache.
- OAuth MV3
  - Use `chrome.identity.launchWebAuthFlow` with redirect URI `https://<extension-id>.chromiumapp.org/`.
  - Google OAuth 2.0 client (Web app with above redirect). PKCE recommended.
  - Manifest: permissions `identity`, `storage`, `sidePanel`; host_permissions for `https://*.googleapis.com/*` and auth URLs.
- Sidebar UI
  - Shadcn components: resizable sidebar, command palette, tabs, dropdown, table, badge, tooltip, dialog, toast.
  - Tailwind v4 via `@tailwindcss/vite`. Dark/Light theme, responsive.
- AI Agent (Cloudflare Agents)
  - Cloudflare Worker hosting agent with tool registry for Gmail actions.
  - Use AI Gateway Universal Endpoint with provider `compat` endpoint `chat/completions` and model `google-ai-studio/gemini-2.5-flash-lite` (configurable).
  - Human-in-the-loop for destructive actions (delete, empty trash) via tool approval.
  - Streaming responses for chat UI; summaries and batch triage suggestions.
  - Scheduling: use Agents scheduling API or Cloudflare Workflows for delayed actions (e.g., schedule send fallback).
- Attachments
  - Download and preview supported types via Gmail attachments API.
  - Upload attachments for sending: file picker within extension; send as `message.raw` MIME.
- Schedule Send
  - Option A: Cloudflare Worker + Durable Objects to hold schedule and execute at time.
  - AI Gateway analytics for model usage, retries, caching.
  - Basic logging in Worker (redact PII).

## 6. Non-Functional Requirements
- Performance: First meaningful render < 1.5s on mid-tier device. Inbox listing 50 items < 600ms after token available.
- Accessibility: Keyboard navigation, WCAG AA contrast.
- Security:
  - Least-privilege scopes; avoid `mail.google.com` when possible.
  - Tokens stored per account; encrypt at rest if persisted (note: Chrome storage is not encrypted; consider minimizing refresh token storage; prefer short-lived access tokens and refreshing on demand in background).
  - Never log message bodies or attachments; scrub PII in logs; no analytics of content without opt-in.
- Privacy: All AI prompts must exclude full raw content unless explicitly approved (e.g., per-thread summarization).
- Resilience: AI fallback ordering via AI Gateway; retries with exponential backoff per Gateway config.

## 7. System Architecture
- Chrome Extension (MV3)
  - `background` service worker: OAuth, token refresh, messaging bridge, notifications, context menus, alarms (optional).
  - `side panel` React app (Vite + shadcn + Tailwind v4): inbox UI, thread view, compose, AI chat panel.
  - `content script` (optional): only if we integrate with Gmail web UI (not required for v1).
- Cloudflare Agent Backend
  - Cloudflare Workers + Agents SDK for AI tools, streaming, and scheduling.
  - Tools call Gmail REST using user tokens (sent from extension transiently or stored encrypted in Durable Objects with per-user consent).
  - AI Gateway Universal Endpoint routing to Gemini 2.5 Flash Lite (primary) with fallback list.

## 8. Data Model (local)
- Account: `{ id, email, provider: 'gmail', tokens: { accessToken, refreshToken?, expiry }, profile }`
- Label: `{ id, name, type: system|user, color? }`
- Thread: `{ id, snippet, historyId, messages: Message[], labelIds: string[], unread: boolean }`
- Message: `{ id, threadId, headers, bodyHtml?, bodyText?, attachments: AttachmentMeta[] }`
- AI Task: `{ id, type: summarize|classify|search|generateReply|bulkTriage, status, inputs, outputs }`

## 9. AI Agent Behavior
- Prompts
  - Summarize: “Summarize this thread concisely with key decisions and action items.”
  - Classify/Label: “Suggest labels for this message using predefined labels; respond JSON with {labels:[…]}.”
  - Search assistant: “Transform natural language to Gmail query string (e.g., from:, has:attachment).”
  - Generate reply: “Draft a professional reply based on thread context. Obey ‘To/Cc/Bcc’ policies.”
- Tools
  - `gmail.list`, `gmail.getThread`, `gmail.modifyLabels`, `gmail.send`, `gmail.draft`, `gmail.attachments.get`, `gmail.trash`, `gmail.emptyTrash`.
  - Approval required: `trash`, `emptyTrash`, batch triage.
- Model & Gateway
  - Primary: Gemini 2.5 Flash Lite via AI Gateway Universal Endpoint `compat/chat/completions`.
  - Fallback: Workers AI model (e.g., `@cf/meta/llama-3.1-8b-instruct`) via Gateway model list, if configured.
  - Streaming responses to UI.

## 10. UI/UX
- Side panel layout
  - Header: account switcher, search bar (NL + Gmail query), quick filters.
  - Tabs: Inbox, Starred, Labels, Scheduled, Trash.
  - List: virtualized list with unread indicators, labels, attachment icon, date.
  - Thread view: message timeline, participants, attachments preview, AI summary block.
  - Compose modal/drawer: to/cc/bcc, subject, body (markdown/rich text), attachments, schedule.
  - AI panel: chat with tool call visualization and approval prompts.

## 11. Tech Stack
- Frontend (extension): React + Vite, Tailwind CSS v4 via `@tailwindcss/vite`, shadcn/ui, TypeScript, Bun.
- Extension APIs: `sidePanel`, `identity.launchWebAuthFlow`, `storage`, `runtime`, `alarms` (optional), `downloads` (optional), `notifications` (optional).
- Backend: Cloudflare Workers + Agents SDK, AI Gateway Universal Endpoint, Optional Workflows or Durable Objects for scheduling.

## 12. Compliance & Store
- Chrome Store: correct permissions copy, privacy policy (token handling), data deletion flow.
- Branding: clear labeling of AI features and data usage.

## 13. Success Metrics
- TTV: median time to link first account < 60s.
- Inbox listing success rate > 99%.
- AI summary latency p50 < 2.5s (short threads), p90 < 5s.
- Reduction of manual triage time by > 40% (self-reported).

## 14. Risks & Mitigations
- OAuth friction in MV3: Mitigate with clear consent flow and tested redirect.
- Token storage in extension: Prefer short-lived tokens and avoid refresh tokens where possible; consider backend storage with encryption when AI tools need server execution.
- Gmail API quotas: Use incremental sync (history API in future), aggressive pagination, and batchModify.
- Model drift/costs: Use Gateway analytics, set retries/backoff, enable caching where safe.

## 15. Open Questions
- Backend token storage vs client-only? (Security vs simplicity)
- Schedule send: Will we rely on Cloudflare Workflows or keep client-side alarms? Prefer backend.
- Attachment size limits and alternative storage (R2) for drafts?
