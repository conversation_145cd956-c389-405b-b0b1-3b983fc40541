export type Tokens = {
  access_token: string;
  refresh_token?: string;
  scope?: string;
  token_type: string;
  expiry: number; // epoch ms
};

export type Profile = {
  email?: string;
  name?: string;
  picture?: string;
  sub?: string; // OIDC subject
};

export type Account = {
  id: string; // prefer email or OIDC sub
  provider: 'gmail';
  email: string;
  tokens: Tokens;
  profile?: Profile;
};

export type AccountsState = {
  accounts: Account[];
  activeAccountId?: string;
};
