/* eslint-disable @typescript-eslint/no-explicit-any */
import { GOO<PERSON><PERSON>_AUTH_ENDPOINT, GOO<PERSON><PERSON>_TOKEN_ENDPOINT, GOOGLE_REVOKE_ENDPOINT, GMAIL_PROFILE_ENDPOINT, OPENID_USERINFO_ENDPOINT, TOKEN_EXPIRY_SKEW_SECONDS, USE_BACKEND_TOKEN_EXCHANGE, BACKEND_BASE_URL } from "./config";
import type { Account, Tokens, Profile } from "./types";

function base64urlEncode(buf: ArrayBuffer): string {
  const bytes = new Uint8Array(buf);
  let str = "";
  for (let i = 0; i < bytes.byteLength; i++) str += String.fromCharCode(bytes[i]);
  return btoa(str).replace(/\+/g, "-").replace(/\//g, "_").replace(/=+$/, "");
}

async function sha256(input: string): Promise<ArrayBuffer> {
  const encoder = new TextEncoder();
  const data = encoder.encode(input);
  return await crypto.subtle.digest("SHA-256", data);
}

export async function generatePKCE() {
  const codeVerifier = base64urlEncode(crypto.getRandomValues(new Uint8Array(32)).buffer);
  const challengeBuffer = await sha256(codeVerifier);
  const codeChallenge = base64urlEncode(challengeBuffer);
  return { codeVerifier, codeChallenge };
}

export function getRedirectUri(): string {
  // MV3 extension redirect — prefer Chrome API helper, fallback to manual
  try {
    return chrome.identity.getRedirectURL();
  } catch {
    return `https://${chrome.runtime.id}.chromiumapp.org/`;
  }
}

export function buildAuthUrl(params: {
  clientId: string;
  redirectUri: string;
  scope: string; // space-separated
  state: string;
  codeChallenge: string;
}) {
  const url = new URL(GOOGLE_AUTH_ENDPOINT);
  url.searchParams.set("client_id", params.clientId);
  url.searchParams.set("redirect_uri", params.redirectUri);
  url.searchParams.set("response_type", "code");
  url.searchParams.set("scope", params.scope);
  url.searchParams.set("state", params.state);
  url.searchParams.set("code_challenge", params.codeChallenge);
  url.searchParams.set("code_challenge_method", "S256");
  url.searchParams.set("access_type", "offline");
  url.searchParams.set("include_granted_scopes", "true");
  url.searchParams.set("prompt", "consent"); // ensure refresh_token on first grant
  return url.toString();
}

export function parseAuthCodeFromRedirect(redirectUrl: string): { code?: string; state?: string; error?: string; error_description?: string } {
  const url = new URL(redirectUrl);
  // Google typically sends in query, but be defensive if fragment
  const query = url.searchParams;
  const hash = new URLSearchParams(url.hash.replace(/^#/, ""));
  const code = query.get("code") || hash.get("code") || undefined;
  const state = query.get("state") || hash.get("state") || undefined;
  const error = query.get("error") || hash.get("error") || undefined;
  const error_description = query.get("error_description") || hash.get("error_description") || undefined;
  return { code: code ?? undefined, state: state ?? undefined, error: error ?? undefined, error_description: error_description ?? undefined };
}

export async function exchangeCodeForTokens(args: {
  clientId: string;
  code: string;
  codeVerifier: string;
  redirectUri: string;
}): Promise<Tokens> {
  const useBackend = USE_BACKEND_TOKEN_EXCHANGE === true;
  const res = await (async () => {
    if (useBackend) {
      // Delegate to backend to attach client_secret securely
      return await fetch(`${BACKEND_BASE_URL}/oauth/google/token`, {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ code: args.code, code_verifier: args.codeVerifier, redirect_uri: args.redirectUri }),
      });
    }
    const body = new URLSearchParams();
    body.set("client_id", args.clientId);
    body.set("grant_type", "authorization_code");
    body.set("code", args.code);
    body.set("code_verifier", args.codeVerifier);
    body.set("redirect_uri", args.redirectUri);
    return await fetch(GOOGLE_TOKEN_ENDPOINT, {
      method: "POST",
      headers: { "Content-Type": "application/x-www-form-urlencoded" },
      body,
    });
  })();
  if (!res.ok) {
    const text = await res.text();
    throw new Error(`Token exchange failed: ${res.status} ${text}`);
  }
  const json = (await res.json()) as any;
  const expiresIn = typeof json.expires_in === "number" ? json.expires_in : 0;
  const expiry = Date.now() + (expiresIn * 1000) - TOKEN_EXPIRY_SKEW_SECONDS * 1000;
  const tokens: Tokens = {
    access_token: json.access_token,
    refresh_token: json.refresh_token,
    scope: json.scope,
    token_type: json.token_type,
    expiry,
  };
  return tokens;
}

export async function refreshTokens(args: { clientId: string; refresh_token: string }): Promise<Tokens> {
  const useBackend = USE_BACKEND_TOKEN_EXCHANGE === true;
  const res = await (async () => {
    if (useBackend) {
      return await fetch(`${BACKEND_BASE_URL}/oauth/google/refresh`, {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ refresh_token: args.refresh_token }),
      });
    }
    const body = new URLSearchParams();
    body.set("client_id", args.clientId);
    body.set("grant_type", "refresh_token");
    body.set("refresh_token", args.refresh_token);
    return await fetch(GOOGLE_TOKEN_ENDPOINT, {
      method: "POST",
      headers: { "Content-Type": "application/x-www-form-urlencoded" },
      body,
    });
  })();
  if (!res.ok) {
    const text = await res.text();
    throw new Error(`Token refresh failed: ${res.status} ${text}`);
  }
  const json = (await res.json()) as any;
  const expiresIn = typeof json.expires_in === "number" ? json.expires_in : 0;
  const expiry = Date.now() + (expiresIn * 1000) - TOKEN_EXPIRY_SKEW_SECONDS * 1000;
  const tokens: Tokens = {
    access_token: json.access_token,
    // Some providers do not return refresh_token on refresh
    refresh_token: args.refresh_token,
    scope: json.scope,
    token_type: json.token_type,
    expiry,
  };
  return tokens;
}

export async function revokeToken(token: string) {
  const body = new URLSearchParams();
  body.set("token", token);
  const res = await fetch(GOOGLE_REVOKE_ENDPOINT, {
    method: "POST",
    headers: { "Content-Type": "application/x-www-form-urlencoded" },
    body,
  });
  // Google's revoke often returns 200 with empty body; ignore non-200 silently
  return res.ok;
}

export async function fetchGmailProfile(accessToken: string): Promise<{ emailAddress?: string; }> {
  const res = await fetch(GMAIL_PROFILE_ENDPOINT, {
    headers: { Authorization: `Bearer ${accessToken}` },
  });
  if (!res.ok) throw new Error(`Profile fetch failed: ${res.status}`);
  const json = await res.json();
  return { emailAddress: json.emailAddress };
}

export async function fetchOidcUserInfo(accessToken: string): Promise<Profile> {
  const res = await fetch(OPENID_USERINFO_ENDPOINT, {
    headers: { Authorization: `Bearer ${accessToken}` },
  });
  if (!res.ok) return {};
  const json = (await res.json()) as any;
  return {
    email: json.email,
    name: json.name,
    picture: json.picture,
    sub: json.sub,
  };
}
