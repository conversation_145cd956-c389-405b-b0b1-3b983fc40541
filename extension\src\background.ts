/// <reference types="chrome" />

import { GOOGLE_CLIENT_ID, OAUTH_SCOPES } from "./lib/config";
import {
  generatePKCE,
  buildAuthUrl,
  parseAuthCodeFromRedirect,
  exchangeCodeForTokens,
  refreshTokens,
  fetchGmailProfile,
  fetchOidcUserInfo,
  revokeToken,
  getRedirectUri,
} from "./lib/oauth";
import {
  getAccountsState,
  upsertAccount,
  removeAccount,
  setActiveAccount,
  findAccount,
} from "./lib/storage";
import type { Account, Tokens } from "./lib/types";

// Background service worker for MV3
chrome.runtime.onInstalled.addListener(() => {
  console.log("Gmail AI Agent Sidebar: installed");
});

// Ensure the side panel is enabled everywhere and opens on toolbar click
async function enableSidePanelForAllTabs() {
  if (!chrome.sidePanel || !chrome.sidePanel.setOptions) return;
  try {
    const tabs = await chrome.tabs.query({});
    await Promise.allSettled(
      tabs.map((tab) =>
        tab.id
          ? chrome.sidePanel!.setOptions({ tabId: tab.id, path: "sidepanel.html", enabled: true })
          : Promise.resolve()
      )
    );
  } catch (e) {
    console.warn("Failed to enable side panel for all tabs", e);
  }
}

// Open panel on action click when supported
if (chrome.sidePanel && (chrome.sidePanel as any).setPanelBehavior) {
  try {
    (chrome.sidePanel as any).setPanelBehavior({ openPanelOnActionClick: true });
  } catch (e) {
    console.warn("setPanelBehavior not available", e);
  }
}

// Fallback: if user clicks the action, try to ensure panel is enabled for the active tab
chrome.action.onClicked.addListener(async (tab) => {
  if (!chrome.sidePanel || !chrome.sidePanel.setOptions) return;
  try {
    if (tab.id) {
      await chrome.sidePanel.setOptions({ tabId: tab.id, path: "sidepanel.html", enabled: true });
      // Some Chrome versions open automatically if setPanelBehavior is on
      // Otherwise, user can open from the Side panel button in the toolbar
    }
  } catch (e) {
    console.warn("Failed to set side panel options on action click", e);
  }
});

chrome.runtime.onStartup.addListener(async () => {
  await ensureSomeActiveAccount();
});

chrome.runtime.onInstalled.addListener(async () => {
  await enableSidePanelForAllTabs();
  await ensureSomeActiveAccount();
});

chrome.tabs.onActivated.addListener(async () => {
  await enableSidePanelForAllTabs();
});

chrome.tabs.onUpdated.addListener(async (_tabId, info) => {
  if (info.status === "complete") {
    await enableSidePanelForAllTabs();
  }
});

// -----------------------
// OAuth + Accounts Logic
// -----------------------

function randomState(): string {
  const arr = new Uint8Array(16);
  crypto.getRandomValues(arr);
  return Array.from(arr, (b) => b.toString(16).padStart(2, "0")).join("");
}

async function addAccountFlow(): Promise<Account> {
  console.log("[auth] addAccountFlow: start");
  const { codeVerifier, codeChallenge } = await generatePKCE();
  const state = randomState();
  const redirectUri = getRedirectUri();
  console.log("[auth] redirectUri:", redirectUri);
  const authUrl = buildAuthUrl({
    clientId: GOOGLE_CLIENT_ID,
    redirectUri,
    scope: OAUTH_SCOPES,
    state,
    codeChallenge,
  });

  const redirect = await chrome.identity.launchWebAuthFlow({
    url: authUrl,
    interactive: true,
  });
  console.log("[auth] launchWebAuthFlow redirect received");

  const parsed = parseAuthCodeFromRedirect(redirect);
  console.log("[auth] redirect parsed:", {
    hasCode: Boolean(parsed.code),
    hasState: Boolean(parsed.state),
    error: parsed.error,
    error_description: parsed.error_description,
  });
  const { code, state: returnedState } = parsed;
  if (!code) {
    const reason = parsed.error ? `${parsed.error}${parsed.error_description ? `: ${parsed.error_description}` : ""}` : "missing_code";
    console.error("[auth] No authorization code returned:", reason);
    throw new Error(`No authorization code returned: ${reason}`);
  }
  if (returnedState && returnedState !== state) throw new Error("State mismatch");

  let tokens: Tokens;
  try {
    tokens = await exchangeCodeForTokens({
      clientId: GOOGLE_CLIENT_ID,
      code,
      codeVerifier,
      redirectUri,
    });
  } catch (e: any) {
    console.error("[auth] token exchange failed:", e?.message || String(e));
    throw e;
  }
  console.log("[auth] token exchange ok; has_refresh:", Boolean(tokens.refresh_token), "scope:", tokens.scope);

  // Fetch profile info
  const gmail = await fetchGmailProfile(tokens.access_token).catch(() => ({ emailAddress: undefined }));
  const oidc = await fetchOidcUserInfo(tokens.access_token).catch(() => ({}));
  console.log("[auth] profile fetched:", { gmailEmail: gmail.emailAddress, oidcEmail: (oidc as any).email });

  const email = gmail.emailAddress || oidc.email || "";
  const id = email || oidc.sub || `acct_${Date.now()}`;

  const account: Account = {
    id,
    provider: "gmail",
    email: email,
    tokens,
    profile: {
      email: email || oidc.email,
      name: oidc.name,
      picture: oidc.picture,
      sub: oidc.sub,
    },
  };

  await upsertAccount(account, true);
  console.log("[auth] account upserted:", { id: account.id, email: account.email });
  return account;
}

async function ensureValidAccessToken(accountId: string): Promise<Tokens | undefined> {
  const acc = await findAccount(accountId);
  if (!acc) return undefined;
  if (acc.tokens.expiry && Date.now() < acc.tokens.expiry) return acc.tokens;
  if (!acc.tokens.refresh_token) return acc.tokens; // nothing we can do
  try {
    const refreshed = await refreshTokens({ clientId: GOOGLE_CLIENT_ID, refresh_token: acc.tokens.refresh_token });
    const updated: Account = { ...acc, tokens: refreshed };
    await upsertAccount(updated, acc.id === (await getAccountsState()).activeAccountId);
    return refreshed;
  } catch (e) {
    console.warn("Token refresh failed for account", acc.id, e);
    return acc.tokens;
  }
}

async function signOutAccount(accountId: string): Promise<void> {
  const acc = await findAccount(accountId);
  if (!acc) return;
  try {
    if (acc.tokens.access_token) await revokeToken(acc.tokens.access_token);
    if (acc.tokens.refresh_token) await revokeToken(acc.tokens.refresh_token);
  } catch {
    // ignore
  }
  await removeAccount(accountId);
}

// -----------------------
// Messaging API
// -----------------------

type MessageRequest =
  | { type: "accounts/list" }
  | { type: "accounts/add" }
  | { type: "accounts/switch"; accountId?: string }
  | { type: "accounts/signout"; accountId?: string }
  | { type: "tokens/getActive" };

function notifyAccountsChanged() {
  try {
    chrome.runtime.sendMessage({ type: "accounts/changed" });
  } catch {
    // ignore
  }
}

async function ensureSomeActiveAccount(): Promise<void> {
  const state = await getAccountsState();
  if (!state.activeAccountId && state.accounts.length > 0) {
    const first = state.accounts[0].id;
    await setActiveAccount(first);
    notifyAccountsChanged();
  }
}

chrome.runtime.onMessage.addListener((message: MessageRequest, _sender, sendResponse) => {
  (async () => {
    try {
      switch (message.type) {
        case "accounts/list": {
          await ensureSomeActiveAccount();
          const state = await getAccountsState();
          sendResponse({ ok: true, state });
          break;
        }
        case "accounts/add": {
          const account = await addAccountFlow();
          const state = await getAccountsState();
          notifyAccountsChanged();
          sendResponse({ ok: true, account, state });
          break;
        }
        case "accounts/switch": {
          const state = await setActiveAccount(message.accountId);
          // opportunistically refresh
          if (state.activeAccountId) await ensureValidAccessToken(state.activeAccountId);
          notifyAccountsChanged();
          sendResponse({ ok: true, state });
          break;
        }
        case "accounts/signout": {
          const state = await getAccountsState();
          const targetId = message.accountId ?? state.activeAccountId;
          if (targetId) await signOutAccount(targetId);
          const newState = await getAccountsState();
          notifyAccountsChanged();
          sendResponse({ ok: true, state: newState });
          break;
        }
        case "tokens/getActive": {
          await ensureSomeActiveAccount();
          const state = await getAccountsState();
          const id = state.activeAccountId;
          if (!id) return sendResponse({ ok: false, error: "no_active_account" });
          const tokens = await ensureValidAccessToken(id);
          sendResponse({ ok: true, accessToken: tokens?.access_token });
          break;
        }
        default:
          sendResponse({ ok: false, error: "unknown_message" });
      }
    } catch (e: any) {
      sendResponse({ ok: false, error: e?.message || String(e) });
    }
  })();
  return true; // keep channel open for async
});
