import React, { useEffect, useMemo, useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Inbox, Star, Tag, Clock, Trash2, Plus, LogOut } from "lucide-react";
import { addAccount, listAccounts, signOut, switchAccount } from "@/lib/bridge";
import type { AccountsState } from "@/lib/types";
import { getLabel } from "@/lib/gmail";

type NavItem = {
  key: string;
  label: string;
  icon: React.ReactNode;
};

const NAV: NavItem[] = [
  { key: "inbox", label: "Inbox", icon: <Inbox className="size-4" /> },
  { key: "starred", label: "Starred", icon: <Star className="size-4" /> },
  { key: "labels", label: "Labels", icon: <Tag className="size-4" /> },
  { key: "scheduled", label: "Scheduled", icon: <Clock className="size-4" /> },
  { key: "trash", label: "Trash", icon: <Trash2 className="size-4" /> },
];

export function Sidebar() {
  const [state, setState] = useState<AccountsState>({ accounts: [], activeAccountId: undefined });
  const active = useMemo(
    () => state.accounts.find((a) => a.id === state.activeAccountId),
    [state]
  );
  const [inboxUnread, setInboxUnread] = useState<number>(0);

  useEffect(() => {
    listAccounts().then((res) => {
      if (res.ok && res.state) setState(res.state);
    });
  }, []);

  // auto-refresh when background notifies
  useEffect(() => {
    const handler = (msg: any) => {
      if (msg?.type === "accounts/changed" || msg?.type === "mail/updated") {
        listAccounts().then((res) => {
          if (res.ok && res.state) setState(res.state);
        });
        // refresh inbox unread
        refreshUnread();
      }
    };
    chrome.runtime.onMessage.addListener(handler);
    return () => chrome.runtime.onMessage.removeListener(handler);
  }, []);

  useEffect(() => {
    // refresh unread on mount and when active account changes
    refreshUnread();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [state.activeAccountId]);

  const refreshUnread = async () => {
    try {
      const label = await getLabel("INBOX");
      const n = (label?.threadsUnread ?? label?.messagesUnread ?? 0) as number;
      setInboxUnread(typeof n === "number" ? n : 0);
    } catch {
      setInboxUnread(0);
    }
  };

  const handleAdd = async () => {
    const res = await addAccount();
    if (res.ok && res.state) {
      setState(res.state);
      // Ensure active is set (defensive) and trigger refresh
      if (res.state.activeAccountId) {
        await switchAccount(res.state.activeAccountId);
      }
    }
  };

  const handleSwitch = async (accountId?: string) => {
    const res = await switchAccount(accountId);
    if (res.ok && res.state) setState(res.state);
  };

  const handleSignOut = async (accountId?: string) => {
    const res = await signOut(accountId);
    if (res.ok && res.state) setState(res.state);
  };

  const avatarSrc = active?.profile?.picture || "";
  const avatarFallback = (active?.email || "U").charAt(0).toUpperCase();

  return (
    <aside className="h-full w-full bg-background flex flex-col items-center py-2">
      <nav className="flex flex-col items-center space-y-2">
        {NAV.map((item) => (
          <div key={item.key} className="relative">
            <Button
              variant="ghost"
              size="sm"
              className="w-8 h-8 p-0 justify-center"
              title={item.label}
            >
              {item.icon}
            </Button>
            {item.key === "inbox" && inboxUnread > 0 && (
              <span className="absolute -top-1 -right-1 min-w-4 h-4 px-1 rounded-full bg-primary text-primary-foreground text-[10px] leading-4 text-center">
                {inboxUnread > 99 ? "99+" : inboxUnread}
              </span>
            )}
          </div>
        ))}
      </nav>
      <div className="mt-auto flex flex-col items-center">
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant="ghost" className="w-8 h-8 p-0 rounded-full">
              <Avatar className="size-7">
                <AvatarImage src={avatarSrc} alt={active?.email || "user"} />
                <AvatarFallback>{avatarFallback}</AvatarFallback>
              </Avatar>
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end" className="w-56">
            <DropdownMenuItem className="flex items-center gap-2">
              <div className="flex flex-col">
                <span className="text-sm font-medium">{active?.email || "No account"}</span>
                <span className="text-xs opacity-70">Switch account</span>
              </div>
            </DropdownMenuItem>
            {/* List all accounts to switch */}
            {state.accounts.length > 0 && (
              <>
                <DropdownMenuSeparator />
                {state.accounts.map((a) => (
                  <DropdownMenuItem
                    key={a.id}
                    className="flex items-center gap-2"
                    onSelect={() => handleSwitch(a.id)}
                  >
                    <span className="truncate">{a.email}</span>
                    {a.id === state.activeAccountId && (
                      <span className="ml-auto text-xs opacity-70">Active</span>
                    )}
                  </DropdownMenuItem>
                ))}
              </>
            )}
            <DropdownMenuSeparator />
            <DropdownMenuItem className="flex items-center gap-2" onSelect={handleAdd}>
              <Plus className="size-4" />
              <span>Add Account</span>
            </DropdownMenuItem>
            {active && (
              <DropdownMenuItem className="flex items-center gap-2" onSelect={() => handleSignOut()}>
                <LogOut className="size-4" />
                <span>Sign out</span>
              </DropdownMenuItem>
            )}
          </DropdownMenuContent>
        </DropdownMenu>
      </div>
    </aside>
  );
}
