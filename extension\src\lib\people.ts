import { getActiveAccessToken } from "./bridge";

const PEOPLE_BASE = "https://people.googleapis.com/v1" as const;
const GOOGLE_S2_PHOTO = (email: string, size = 64) => `https://www.google.com/s2/photos/profile/${encodeURIComponent(email)}?sz=${size}`;
const CLEARBIT_LOGO = (domain: string, size = 64) => `https://logo.clearbit.com/${domain}?size=${size}`;
const GOOGLE_FAVICON = (domain: string, size = 64) => `https://www.google.com/s2/favicons?domain=${encodeURIComponent(domain)}&sz=${size}`;

type Person = {
  names?: { displayName?: string }[];
  photos?: { url?: string; default?: boolean }[];
  emailAddresses?: { value?: string }[];
};

async function ensureAccessToken(): Promise<string> {
  const res = await getActiveAccessToken();
  if (!res.ok || !res.accessToken) throw new Error(res.error || "no_active_token");
  return res.accessToken;
}

export function getDomainLogoUrl(email: string | undefined, size = 64): string | undefined {
  if (!email) return undefined;
  const at = email.indexOf("@");
  if (at === -1) return undefined;
  const domain = email.slice(at + 1).trim();
  if (!domain) return undefined;
  return CLEARBIT_LOGO(domain, size);
}

export function getGoogleFaviconUrl(email: string | undefined, size = 64): string | undefined {
  if (!email) return undefined;
  const at = email.indexOf("@");
  if (at === -1) return undefined;
  const domain = email.slice(at + 1).trim();
  if (!domain) return undefined;
  return GOOGLE_FAVICON(domain, size);
}

async function authFetch(url: string, init?: RequestInit): Promise<Response> {
  const token = await ensureAccessToken();
  const res = await fetch(url, {
    ...(init || {}),
    headers: {
      ...(init?.headers || {}),
      Authorization: `Bearer ${token}`,
    },
  });
  if (!res.ok) {
    const text = await res.text().catch(() => "");
    throw new Error(`People API ${url} failed: ${res.status} ${text}`);
  }
  return res;
}

function pickBestPhotoUrl(p: Person | undefined): string | undefined {
  const photos = p?.photos || [];
  // Prefer non-default (actual) photos first, fallback to default silhouette
  const nonDefault = photos.find((x) => !x.default) || photos[0];
  return nonDefault?.url;
}

export async function searchContactByEmail(email: string): Promise<{ name?: string; photoUrl?: string } | undefined> {
  if (!email) return undefined;
  const q = encodeURIComponent(email);
  const readMask = encodeURIComponent("names,photos,emailAddresses");
  // Try contacts first
  try {
    const url = `${PEOPLE_BASE}/people:searchContacts?query=${q}&pageSize=1&readMask=${readMask}`;
    const res = await authFetch(url);
    const json = await res.json();
    const person: Person | undefined = json?.results?.[0]?.person;
    const name = person?.names?.[0]?.displayName;
    const photoUrl = pickBestPhotoUrl(person);
    if (person) return { name, photoUrl };
  } catch {
    // ignore and try otherContacts
  }
  // Try other contacts (auto-saved)
  try {
    const url = `${PEOPLE_BASE}/otherContacts:search?query=${q}&pageSize=1&readMask=${readMask}`;
    const res = await authFetch(url);
    const json = await res.json();
    const person: Person | undefined = json?.results?.[0]?.person;
    const name = person?.names?.[0]?.displayName;
    const photoUrl = pickBestPhotoUrl(person);
    if (person) return { name, photoUrl };
  } catch {
    // ignore
  }
  return undefined;
}

export async function fetchContactPhotoObjectUrl(email: string): Promise<string | undefined> {
  const found = await searchContactByEmail(email);
  const photoUrl = found?.photoUrl;
  if (!photoUrl) {
    // Fallback to Google s2 public profile photo endpoint
    return GOOGLE_S2_PHOTO(email);
  }
  // Try fetch as blob with Authorization header first
  try {
    const res = await authFetch(photoUrl);
    const blob = await res.blob();
    const objectUrl = URL.createObjectURL(blob);
    return objectUrl;
  } catch {
    // Fallback: append access_token to URL and return direct URL (may be cached by browser)
    try {
      const token = await ensureAccessToken();
      const sep = photoUrl.includes("?") ? "&" : "?";
      return `${photoUrl}${sep}access_token=${encodeURIComponent(token)}`;
    } catch {
      return undefined;
    }
  }
}
