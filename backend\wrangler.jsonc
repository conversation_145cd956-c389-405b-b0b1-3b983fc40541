{
  // Wrangler configuration in JSONC (JSON with comments)
  "name": "gmail-agent-backend",
  "main": "src/index.ts",
  "compatibility_date": "2024-12-01",

  "observability": {
    "enabled": true
  },

  // Non-sensitive key/values can go here; secrets should be set via `wrangler secret put` or `.dev.vars`
  "vars": {
     // "GOOGLE_CLIENT_ID": "",
     // "GOOGLE_CLIENT_SECRET": ""
  },

  "dev": {
    "port": 8787
  }
}
