import React, { useEffect, useMemo, useRef, useState } from "react";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Button } from "@/components/ui/button";
import { useVirtualizer } from "@tanstack/react-virtual";
import {
  getThread,
  listThreads,
  computeThreadPreview,
  CATEGORY_LABEL_IDS,
  markThreadRead,
  markThreadUnread,
} from "@/lib/gmail";
import { listAccounts, switchAccount } from "@/lib/bridge";
import { Mail, MailOpen, ChevronLeft, ChevronRight, Filter } from "lucide-react";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
  DropdownMenuLabel,
  DropdownMenuSeparator,
} from "@/components/ui/dropdown-menu";
import { fetchContactPhotoObjectUrl, searchContactByEmail, getDomainLogoUrl, getGoogleFaviconUrl } from "@/lib/people";

type ThreadView = {
  id: string;
  from: string;
  subject: string;
  preview: string;
  date: string;
  unread?: boolean;
};

type CategoryKey = "all" | "promotions" | "spam" | "primary" | "social" | "updates" | "forums";
type FilterType = "all" | "unread" | "read";

// Format email date header into a friendly short string
function formatFriendlyDate(dateStr: string): string {
  if (!dateStr) return "";
  const d = new Date(dateStr);
  if (isNaN(d.getTime())) return "";
  const now = new Date();
  const isSameDay =
    d.getFullYear() === now.getFullYear() &&
    d.getMonth() === now.getMonth() &&
    d.getDate() === now.getDate();
  const yest = new Date(now);
  yest.setDate(now.getDate() - 1);
  const isYesterday =
    d.getFullYear() === yest.getFullYear() &&
    d.getMonth() === yest.getMonth() &&
    d.getDate() === yest.getDate();
  if (isSameDay) {
    const time = new Intl.DateTimeFormat(undefined, { hour: "2-digit", minute: "2-digit" }).format(d);
    return `Today ${time}`;
  }
  if (isYesterday) {
    const time = new Intl.DateTimeFormat(undefined, { hour: "2-digit", minute: "2-digit" }).format(d);
    return `Yesterday ${time}`;
  }
  if (d.getFullYear() === now.getFullYear()) {
    return new Intl.DateTimeFormat(undefined, { day: "2-digit", month: "short" }).format(d);
  }
  return new Intl.DateTimeFormat(undefined, { day: "2-digit", month: "short", year: "numeric" }).format(d);
}

// Parse a RFC 2822-like From header to name/email/initial
function parseFromHeader(from: string | undefined): { name: string; email: string; initial: string } {
  if (!from) return { name: "", email: "", initial: "?" };
  let name = "";
  let email = "";
  const m = from.match(/\"?([^"<]*)\"?\s*<([^>]+)>/);
  if (m) {
    name = (m[1] || "").trim();
    email = (m[2] || "").trim();
  } else {
    const trimmed = from.trim();
    // Try to extract email part if present
    const m2 = trimmed.match(/([^\s]+@[^\s>]+)/);
    if (m2) email = m2[1].trim();
    if (!email && trimmed.includes("@")) email = trimmed;
    if (!name && email) name = email.split("@")[0];
  }
  const initial = ((name || email || "?").trim().charAt(0) || "?").toUpperCase();
  return { name, email, initial };
}

function useGmailInbox() {
  const [items, setItems] = useState<ThreadView[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | undefined>(undefined);
  const [category, setCategory] = useState<CategoryKey>("all");
  const [filterType, setFilterType] = useState<FilterType>("all");
  const [pageToken, setPageToken] = useState<string | undefined>(undefined);
  const [nextPageToken, setNextPageToken] = useState<string | undefined>(undefined);
  const [tokensStack, setTokensStack] = useState<string[]>([]);

  const fetchInbox = async (retried = false) => {
    setLoading(true);
    setError(undefined);
    try {
      let labelIds: string[] = [];
      let includeSpamTrash = false;
      if (category === "spam") {
        labelIds = ["SPAM"];
        includeSpamTrash = true;
      } else if (category === "all") {
        labelIds = ["INBOX"];
      } else if (category === "promotions") {
        labelIds = ["INBOX", (CATEGORY_LABEL_IDS as any)[category]];
      } else {
        // Other categories selected via filter menu
        labelIds = ["INBOX", (CATEGORY_LABEL_IDS as any)[category]];
      }
      const q = filterType === "unread" ? "is:unread" : filterType === "read" ? "is:read" : undefined;
      // Partial response: only ids+snippet+history for list; avoids payload bloat
      const list = await listThreads({
        labelIds,
        maxResults: 50,
        pageToken,
        q,
        includeSpamTrash,
        fields: "threads(id,snippet,historyId),nextPageToken,resultSizeEstimate",
      });
      setNextPageToken(list.nextPageToken);
      const ids = (list.threads ?? []).map((t) => t.id).filter(Boolean) as string[];

      // Seed placeholders immediately for snappy UI
      const initial: ThreadView[] = (list.threads ?? []).map((t) => ({
        id: t.id!,
        from: "",
        subject: "",
        preview: t.snippet || "",
        date: "",
        unread: undefined,
      }));
      setItems(initial);

      // Fetch details concurrently and update incrementally
      const concurrency = 8;
      for (let i = 0; i < ids.length; i += concurrency) {
        const slice = ids.slice(i, i + concurrency);
        const chunk = await Promise.all(
          slice.map(async (id) => {
            const t = await getThread(id, {
              format: "metadata",
              fields: "id,snippet,historyId,messages(id,labelIds,snippet,payload/headers(name,value))",
            });
            const p = computeThreadPreview(t);
            return {
              id,
              from: p.from,
              subject: p.subject,
              preview: p.snippet,
              date: p.date,
              unread: p.unread,
            } as ThreadView;
          })
        );
        setItems((prev) => {
          const map = new Map(prev.map((x) => [x.id, x]));
          for (const item of chunk) map.set(item.id, { ...map.get(item.id), ...item });
          return Array.from(map.values());
        });
      }
    } catch (e: any) {
      const msg = e?.message || String(e);
      // Auto-recover: if no active account but accounts exist, switch to first and retry once
      if (msg.includes("no_active_account") && !retried) {
        try {
          const res = await listAccounts();
          const first = res.state?.accounts?.[0];
          if (first?.id) {
            await switchAccount(first.id);
            return fetchInbox(true);
          }
        } catch {}
      }
      setError(msg);
      setItems([]);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchInbox();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [category, filterType, pageToken]);

  useEffect(() => {
    const handler = (msg: any) => {
      if (msg?.type === "accounts/changed" || msg?.type === "mail/updated") {
        // reset and refetch
        setPageToken(undefined);
        setTokensStack([]);
        fetchInbox();
      }
    };
    chrome.runtime.onMessage.addListener(handler);
    return () => chrome.runtime.onMessage.removeListener(handler);
  }, []);

  const resetAndRefetch = () => {
    setPageToken(undefined);
    setTokensStack([]);
    fetchInbox();
  };

  const goNext = () => {
    if (nextPageToken) {
      setTokensStack((st) => [...st, pageToken || ""]);
      setPageToken(nextPageToken);
    }
  };
  const goPrev = () => {
    setTokensStack((st) => {
      const copy = [...st];
      const prev = copy.pop();
      setPageToken(prev || undefined);
      return copy;
    });
  };

  const setCategoryAndReset = (c: CategoryKey) => {
    setCategory(c);
    setPageToken(undefined);
    setTokensStack([]);
  };

  const setFilterTypeAndReset = (type: FilterType) => {
    setFilterType(type);
    setPageToken(undefined);
    setTokensStack([]);
  };

  return {
    items,
    loading,
    error,
    category,
    filterType,
    hasPrev: tokensStack.length > 0,
    hasNext: Boolean(nextPageToken),
    actions: { refetch: resetAndRefetch, setCategoryAndReset, setFilterTypeAndReset, goNext, goPrev, setItems },
  };
}

export function MailList({ onOpenThread }: { onOpenThread?: (id: string) => void }) {
  const { items, loading, error, category, filterType, hasPrev, hasNext, actions } = useGmailInbox();
  const parentRef = useRef<HTMLDivElement>(null);
  const rowVirtualizer = useVirtualizer({
    count: items.length,
    getScrollElement: () => parentRef.current,
    estimateSize: () => 88, // average row height with increased margin
    overscan: 8,
  });

  // Cache of contact info from People API
  const [contactInfo, setContactInfo] = useState<Record<string, { name?: string; photoUrl?: string }>>({});
  // Avatar sources with progressive fallbacks per email
  const [avatarSources, setAvatarSources] = useState<Record<string, { idx: number; list: string[] }>>({});

  useEffect(() => {
    // Build unique email list from current items
    const emails = Array.from(
      new Set(
        items
          .map((it) => parseFromHeader(it.from).email)
          .filter((e): e is string => Boolean(e))
      )
    );
    const uncached = emails.filter((e) => !contactInfo[e]);
    if (uncached.length === 0) return;
    let cancelled = false;
    const concurrency = 4;
    (async () => {
      for (let i = 0; i < uncached.length; i += concurrency) {
        const chunk = uncached.slice(i, i + concurrency);
        const results = await Promise.all(
          chunk.map(async (email) => {
            // Always prepare fallbacks regardless of People API success
            const candidates: string[] = [];
            const s2 = email ? `https://www.google.com/s2/photos/profile/${encodeURIComponent(email)}?sz=64` : undefined;
            if (s2) candidates.push(s2);
            const domainLogo = getDomainLogoUrl(email);
            if (domainLogo) candidates.push(domainLogo);
            const favicon = getGoogleFaviconUrl(email);
            if (favicon) candidates.push(favicon);

            let name: string | undefined = undefined;
            let photoUrl: string | undefined = undefined;
            try {
              const meta = await searchContactByEmail(email).catch(() => undefined);
              name = meta?.name;
              try {
                photoUrl = await fetchContactPhotoObjectUrl(email);
                if (photoUrl) {
                  // Prefer People API photo by putting it at the front
                  candidates.unshift(photoUrl);
                }
              } catch {
                // ignore, rely on fallbacks
              }
            } catch {
              // People API not available or no match; rely on fallbacks
            }
            // Deduplicate candidates while preserving order
            const seen = new Set<string>();
            const unique = candidates.filter((u) => (u && !seen.has(u) ? (seen.add(u), true) : false));
            return { email, name, photoUrl, candidates: unique } as { email: string; name?: string; photoUrl?: string; candidates: string[] };
          })
        );
        if (cancelled) return;
        setContactInfo((prev) => {
          const next = { ...prev } as Record<string, { name?: string; photoUrl?: string }>;
          for (const r of results) {
            if (!r) continue;
            next[r.email] = { name: r.name, photoUrl: r.photoUrl };
          }
          return next;
        });
        setAvatarSources((prev) => {
          const next = { ...prev } as Record<string, { idx: number; list: string[] }>;
          for (const r of results) {
            if (!r) continue;
            if (!next[r.email] && r.candidates && r.candidates.length > 0) {
              next[r.email] = { idx: 0, list: r.candidates };
            }
          }
          return next;
        });
      }
    })();
    return () => {
      cancelled = true;
    };
  }, [items, contactInfo]);

  const content = useMemo(() => {
    if (loading) {
      return (
        <div className="px-2 py-2 text-xs opacity-70">Loading inbox…</div>
      );
    }
    if (error) {
      return (
        <div className="px-2 py-2 text-xs text-red-600">{error}</div>
      );
    }
    if (items.length === 0) {
      return (
        <div className="px-2 py-2 text-xs opacity-70">No messages</div>
      );
    }
    return (
      <div ref={parentRef} className="h-[calc(100vh-150px)] overflow-auto">
        <div style={{ height: rowVirtualizer.getTotalSize(), position: "relative" }}>
          {rowVirtualizer.getVirtualItems().map((vi) => {
            const m = items[vi.index];
            return (
              <div
                key={m.id}
                data-index={vi.index}
                ref={rowVirtualizer.measureElement}
                style={{
                  position: "absolute",
                  top: 0,
                  left: 0,
                  width: "100%",
                  transform: `translateY(${vi.start}px)`,
                }}
                className="grid grid-cols-[auto_1fr_auto] items-start gap-3 rounded-md border border-border px-3 py-2 mb-4 mx-2 hover:bg-[color-mix(in_oklab,var(--color-background),#000_3%)]"
                role="button"
                onClick={() => onOpenThread?.(m.id)}
              >
                {/* Avatar using People API photo with progressive fallbacks */}
                {(() => {
                  const info = parseFromHeader(m.from);
                  const alt = info.name || info.email || "Sender";
                  const email = info.email || "";
                  let av = email ? avatarSources[email] : undefined;
                  // If no precomputed sources yet, build quick local fallbacks
                  if (!av && email) {
                    const quick: string[] = [];
                    const s2 = `https://www.google.com/s2/photos/profile/${encodeURIComponent(email)}?sz=64`;
                    quick.push(s2);
                    const domainLogo = getDomainLogoUrl(email);
                    if (domainLogo) quick.push(domainLogo);
                    const favicon = getGoogleFaviconUrl(email);
                    if (favicon) quick.push(favicon);
                    av = { idx: 0, list: quick };
                    // Note: do not set state here to avoid render loop; onError will promote to state when needed
                  }
                  const srcCandidate = av?.list?.[av.idx];
                  return (
                    <Avatar className="size-8">
                      {srcCandidate ? (
                        <AvatarImage
                          key={srcCandidate}
                          src={srcCandidate}
                          alt={alt}
                          onError={() => {
                            if (!email) return;
                            console.warn("avatar load failed", { email, src: srcCandidate });
                            // If not in state yet, initialize with current quick list to allow progression
                            setAvatarSources((prev) => {
                              const curr = prev[email];
                              if (!curr && av) {
                                return { ...prev, [email]: { idx: Math.min(1, av.list.length - 1), list: av.list } };
                              }
                              if (!curr) return prev;
                              if (curr.idx >= curr.list.length - 1) return prev;
                              return { ...prev, [email]: { ...curr, idx: curr.idx + 1 } };
                            });
                          }}
                        />
                      ) : null}
                      <AvatarFallback>{info.initial}</AvatarFallback>
                    </Avatar>
                  );
                })()}
                <div className="min-w-0">
                  {/* Row 1: Sender name */}
                  <div className="truncate text-xs opacity-80">
                    {(() => {
                      const { name, email } = parseFromHeader(m.from);
                      const display = (email && contactInfo[email]?.name) || name || (email ? email.split("@")[0] : "");
                      return display;
                    })()}
                  </div>

                  {/* Row 2: Subject spans full width */}
                  <div className="truncate text-sm">
                    <span className={m.unread ? "font-semibold" : ""}>{m.subject || "(loading…)"}</span>
                  </div>

                  {/* Row 3: Snippet single line like Gmail mobile */}
                  <div className="truncate text-xs opacity-70 leading-snug">
                    {m.preview || ""}
                  </div>
                </div>
                <div className="flex flex-col items-end justify-between h-full">
                  <div className="text-xs opacity-70">{formatFriendlyDate(m.date)}</div>
                  <Button
                    size="sm"
                    variant="ghost"
                    className="w-8 h-8 p-0 mb-1"
                    title={m.unread ? "Mark as read" : "Mark as unread"}
                    onClick={async (e) => {
                      e.stopPropagation();
                      try {
                        if (m.unread) await markThreadRead(m.id);
                        else await markThreadUnread(m.id);
                        // Optimistic update
                        actions.setItems((prev) => prev.map((x) => (x.id === m.id ? { ...x, unread: !m.unread } : x)));
                        // Inform others (e.g., Sidebar) to refresh unread badge
                        try { chrome.runtime.sendMessage({ type: "mail/updated" }); } catch {}
                      } catch (e: any) {
                        console.error("toggle read/unread failed", e?.message || String(e));
                      }
                    }}
                  >
                    {m.unread ? <Mail className="size-4" /> : <MailOpen className="size-4" />}
                  </Button>
                </div>
              </div>
            );
          })}
        </div>
      </div>
    );
  }, [items, loading, error, actions]);

  return (
    <div>
      {/* Filters */}
      <div className="flex items-center justify-between mb-2">
        <div className="flex items-center gap-1">
          <Button
            size="sm"
            variant={category === "all" ? "secondary" : "ghost"}
            className="h-7"
            onClick={() => actions.setCategoryAndReset("all")}
          >
            All
          </Button>
          <Button
            size="sm"
            variant={category === "promotions" ? "secondary" : "ghost"}
            className="h-7"
            onClick={() => actions.setCategoryAndReset("promotions")}
          >
            Promotions
          </Button>
          <Button
            size="sm"
            variant={category === "spam" ? "secondary" : "ghost"}
            className="h-7"
            onClick={() => actions.setCategoryAndReset("spam")}
          >
            Spam
          </Button>
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button
                size="sm"
                variant={filterType !== "all" ? "secondary" : "ghost"}
                className="h-7 w-7 p-0"
                title="Filter by read status"
              >
                <Filter className="size-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuLabel>Categories</DropdownMenuLabel>
              <DropdownMenuItem onClick={() => actions.setCategoryAndReset("primary")}>Primary</DropdownMenuItem>
              <DropdownMenuItem onClick={() => actions.setCategoryAndReset("social")}>Social</DropdownMenuItem>
              <DropdownMenuItem onClick={() => actions.setCategoryAndReset("updates")}>Updates</DropdownMenuItem>
              <DropdownMenuItem onClick={() => actions.setCategoryAndReset("forums")}>Forums</DropdownMenuItem>
              <DropdownMenuSeparator />
              <DropdownMenuLabel>Read status</DropdownMenuLabel>
              <DropdownMenuItem onClick={() => actions.setFilterTypeAndReset("all")}>All</DropdownMenuItem>
              <DropdownMenuItem onClick={() => actions.setFilterTypeAndReset("unread")}>Unread</DropdownMenuItem>
              <DropdownMenuItem onClick={() => actions.setFilterTypeAndReset("read")}>Read</DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </div>
      {content}
      {/* Pagination at bottom */}
      <div className="flex items-center justify-center gap-1 mt-2 pt-2 border-t border-border">
        <Button size="sm" variant="ghost" disabled={!hasPrev} onClick={actions.goPrev} className="h-7 w-7 p-0" title="Previous page">
          <ChevronLeft className="size-4" />
        </Button>
        <Button size="sm" variant="ghost" disabled={!hasNext} onClick={actions.goNext} className="h-7 w-7 p-0" title="Next page">
          <ChevronRight className="size-4" />
        </Button>
      </div>
    </div>
  );
}
