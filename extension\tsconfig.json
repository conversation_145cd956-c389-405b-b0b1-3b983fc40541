{"compilerOptions": {"target": "ES2020", "useDefineForClassFields": true, "lib": ["ES2020", "DOM", "WebWorker"], "module": "ESNext", "skipLibCheck": true, "jsx": "react-jsx", "moduleResolution": "<PERSON><PERSON><PERSON>", "resolveJsonModule": true, "isolatedModules": true, "noEmit": true, "strict": true, "types": ["chrome"], "baseUrl": ".", "paths": {"@/*": ["./src/*"]}}, "include": ["src", "vite.config.ts"]}