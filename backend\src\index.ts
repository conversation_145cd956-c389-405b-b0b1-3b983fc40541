export interface Env {
  GOOGLE_CLIENT_ID: string;
  GOOGLE_CLIENT_SECRET: string;
}

const GOOGLE_TOKEN_ENDPOINT = "https://oauth2.googleapis.com/token";

function corsHeaders(origin?: string) {
  return {
    "Access-Control-Allow-Origin": origin || "*",
    "Access-Control-Allow-Methods": "POST, OPTIONS",
    "Access-Control-Allow-Headers": "Content-Type, Authorization",
  } as Record<string, string>;
}

async function handleOptions(request: Request): Promise<Response> {
  const origin = request.headers.get("Origin") || "*";
  return new Response(null, { status: 204, headers: corsHeaders(origin) });
}

async function handleToken(request: Request, env: Env): Promise<Response> {
  const origin = request.headers.get("Origin") || "*";
  try {
    const { code, code_verifier, redirect_uri } = await request.json<any>();
    if (!code || !code_verifier || !redirect_uri) {
      return new Response(
        JSON.stringify({ error: "invalid_request", error_description: "Missing code/code_verifier/redirect_uri" }),
        { status: 400, headers: { "Content-Type": "application/json", ...corsHeaders(origin) } }
      );
    }
    const body = new URLSearchParams();
    body.set("client_id", env.GOOGLE_CLIENT_ID);
    body.set("client_secret", env.GOOGLE_CLIENT_SECRET);
    body.set("grant_type", "authorization_code");
    body.set("code", code);
    body.set("code_verifier", code_verifier);
    body.set("redirect_uri", redirect_uri);

    const resp = await fetch(GOOGLE_TOKEN_ENDPOINT, {
      method: "POST",
      headers: { "Content-Type": "application/x-www-form-urlencoded" },
      body,
    });
    if (!resp.ok) {
      const errText = await resp.text();
      console.error("[oauth] token endpoint non-200:", resp.status, errText);
      return new Response(errText, {
        status: resp.status,
        headers: { "Content-Type": "application/json", ...corsHeaders(origin) },
      });
    }
    const text = await resp.text();
    return new Response(text, {
      status: resp.status,
      headers: { "Content-Type": "application/json", ...corsHeaders(origin) },
    });
  } catch (e: any) {
    return new Response(
      JSON.stringify({ error: "server_error", error_description: e?.message || String(e) }),
      { status: 500, headers: { "Content-Type": "application/json", ...corsHeaders(origin) } }
    );
  }
}

async function handleRefresh(request: Request, env: Env): Promise<Response> {
  const origin = request.headers.get("Origin") || "*";
  try {
    const { refresh_token } = await request.json<any>();
    if (!refresh_token) {
      return new Response(
        JSON.stringify({ error: "invalid_request", error_description: "Missing refresh_token" }),
        { status: 400, headers: { "Content-Type": "application/json", ...corsHeaders(origin) } }
      );
    }
    const body = new URLSearchParams();
    body.set("client_id", env.GOOGLE_CLIENT_ID);
    body.set("client_secret", env.GOOGLE_CLIENT_SECRET);
    body.set("grant_type", "refresh_token");
    body.set("refresh_token", refresh_token);

    const resp = await fetch(GOOGLE_TOKEN_ENDPOINT, {
      method: "POST",
      headers: { "Content-Type": "application/x-www-form-urlencoded" },
      body,
    });
    const text = await resp.text();
    return new Response(text, {
      status: resp.status,
      headers: { "Content-Type": "application/json", ...corsHeaders(origin) },
    });
  } catch (e: any) {
    return new Response(
      JSON.stringify({ error: "server_error", error_description: e?.message || String(e) }),
      { status: 500, headers: { "Content-Type": "application/json", ...corsHeaders(origin) } }
    );
  }
}

export default {
  async fetch(request: Request, env: Env): Promise<Response> {
    const url = new URL(request.url);

    if (request.method === "OPTIONS") return handleOptions(request);

    if (url.pathname === "/oauth/google/token" && request.method === "POST") {
      return handleToken(request, env);
    }
    if (url.pathname === "/oauth/google/refresh" && request.method === "POST") {
      return handleRefresh(request, env);
    }
    return new Response("Not Found", { status: 404 });
  },
};
